"use client";

import React, { Fragment, useEffect, useState, useCallback, useMemo } from 'react';
import SpkButton from '@/shared/@spk-reusable-components/uielements/spk-button';
import { SpkBadge, LKRCurrencyIcon } from '@/shared/UI/components';
import { CashierTurboPlaceBetDetails, BetDetailsData } from '@/shared/types/user-management-types';
import { useBetDetailsLazyQuery } from '@/shared/query/useBetDetailsQuery';
import { formatSriLankanCurrency, formatUserDate } from '@/shared/utils/userDetailsUtils';

/**
 * Utility function to clean and format market names for user-friendly display
 * Removes technical IDs and formats the market name path
 */
const formatMarketName = (marketName: string): string => {
  if (!marketName) return 'N/A';

  // Remove any trailing IDs or technical identifiers in parentheses/brackets
  let cleaned = marketName.replace(/\s*\([^)]*\)\s*$/, '').replace(/\s*\[[^\]]*\]\s*$/, '');

  // Clean up the arrow separators and extra spaces
  cleaned = cleaned.replace(/\s*->\s*/g, ' → ').trim();

  return cleaned || 'N/A';
};

/**
 * Utility function to extract a short display name from the full market path
 * Takes the last meaningful part of the market name for compact display
 */
const getShortMarketName = (marketName: string): string => {
  const formatted = formatMarketName(marketName);
  const parts = formatted.split(' → ');

  // Return the last part (the actual bet type) or the full name if short
  if (parts.length > 1) {
    return parts[parts.length - 1] || formatted;
  }

  return formatted;
};

/**
 * Props interface for the BetDetailsPopup component
 */
export interface BetDetailsPopupProps {
  /** Whether the popup is open/visible */
  isOpen: boolean;
  /** Function called when user closes the popup */
  onClose: () => void;
  /** Bet details to display */
  betDetails: CashierTurboPlaceBetDetails | null;
  /** Whether to show the popup backdrop (default: false for non-modal overlay) */
  showBackdrop?: boolean;
  /** Additional CSS classes for the popup */
  className?: string;
  /** Auto-close delay in milliseconds (0 to disable, default: 5000) */
  autoCloseDelay?: number;
}

/**
 * BetDetailsPopup Component
 *
 * @deprecated This component has been replaced by BetNotification from '@/shared/UI/components'.
 * Please use BetNotification instead for new implementations.
 *
 * A non-modal popup overlay that displays detailed bet information when a bet is placed
 * via the TurboStars sportsbook WebSocket integration. Features include:
 *
 * - Non-modal overlay design that doesn't block sportsbook interaction
 * - User context validation - only shows for the current user's bets
 * - Auto-close functionality with visual countdown and pause-on-hover
 * - Smooth entrance/exit animations and transitions
 * - Full dark/light theme compatibility
 * - Accessibility features (ESC key, ARIA attributes, focus management)
 * - Responsive design for different screen sizes
 *
 * @param props - The component props
 * @returns JSX element representing the bet details popup
 *
 * @example
 * ```tsx
 * <BetDetailsPopup
 *   isOpen={isBetPopupOpen}
 *   onClose={hideBetPopup}
 *   betDetails={currentBetDetails}
 *   showBackdrop={false}
 *   autoCloseDelay={8000}
 *   className="z-[9999]"
 * />
 * ```
 */
const BetDetailsPopup: React.FC<BetDetailsPopupProps> = ({
  isOpen,
  onClose,
  betDetails,
  showBackdrop = false, // Non-modal overlay by default
  className = '',
  autoCloseDelay = 5000 // Auto-close after 5 seconds by default
}) => {
  // State for API-fetched bet details
  const [apiData, setApiData] = useState<BetDetailsData | null>(null);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [hasAttemptedFetch, setHasAttemptedFetch] = useState(false);

  // Hook for fetching bet details from API
  const { fetchBetDetails } = useBetDetailsLazyQuery();

  // Memoize the transaction key to prevent unnecessary re-renders
  const transactionKey = useMemo(() => {
    return betDetails?.transactionId && betDetails?.provider
      ? `${betDetails.transactionId}-${betDetails.provider}`
      : null;
  }, [betDetails?.transactionId, betDetails?.provider]);

  // Memoize the fetch function to prevent re-creation on every render
  const handleFetchBetDetails = useCallback(async () => {
    if (!betDetails?.transactionId || !betDetails?.provider) return;

    setIsLoadingDetails(true);
    setApiError(null);
    setApiData(null);
    setHasAttemptedFetch(true);

    try {
      const response = await fetchBetDetails(betDetails.transactionId, betDetails.provider);
      setApiData(response.data);
      setApiError(null);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error("❌ Failed to fetch bet details:", {
        transactionId: betDetails.transactionId,
        provider: betDetails.provider,
        error: error instanceof Error ? error.message : 'Unknown error',
        isCircuitBreakerError: error instanceof Error && error.message.includes('Circuit breaker')
      });
      setApiError(error instanceof Error ? error.message : 'Failed to fetch bet details');
      setApiData(null);

      // Auto-close popup after error to prevent infinite loops
      const closeDelay = error instanceof Error && error.message.includes('Circuit breaker') ? 5000 : 3000;
      setTimeout(() => {

        onClose();
      }, closeDelay);
    } finally {
      setIsLoadingDetails(false);
    }
  }, [betDetails?.transactionId, betDetails?.provider, fetchBetDetails, onClose]);

  // Fetch bet details when WebSocket notification is received
  useEffect(() => {
    if (isOpen && transactionKey && !hasAttemptedFetch) {
      handleFetchBetDetails();
    }

    // Reset fetch attempt flag when popup closes or bet details change
    if (!isOpen || !betDetails) {
      setHasAttemptedFetch(false);
      setApiData(null);
      setApiError(null);
      setIsLoadingDetails(false);
    }
  }, [isOpen, transactionKey, hasAttemptedFetch, handleFetchBetDetails, betDetails]);

  // Memoize the escape key handler to prevent re-creation on every render
  const handleEscapeKey = useCallback((event: KeyboardEvent) => {
    if (event.key === 'Escape' && isOpen) {
      onClose();
    }
  }, [isOpen, onClose]);

  // Handle escape key press
  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen, handleEscapeKey]);

  // Auto-close functionality with countdown and pause-on-hover
  const [remainingTime, setRemainingTime] = useState(autoCloseDelay);
  const [isPaused, setIsPaused] = useState(false);

  // Memoize the close handler to prevent unnecessary re-renders
  const handleAutoClose = useCallback(() => {
    onClose();
  }, [onClose]);

  // Memoize pause handlers to prevent re-creation on every render
  const handleMouseEnter = useCallback(() => setIsPaused(true), []);
  const handleMouseLeave = useCallback(() => setIsPaused(false), []);

  // Memoize bet list rendering to prevent unnecessary re-renders
  const renderedBetList = useMemo(() => {
    if (!apiData?.betList || apiData.betList.length === 0) return null;

    return (
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 space-y-2">
        <h4 className="text-sm font-semibold text-gray-900 dark:text-white">Bet Items</h4>
        <div className="space-y-2">
          {apiData.betList.map((item, index) => (
            <div key={item.betId || index} className="bg-white dark:bg-gray-700 rounded p-2 text-xs">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <p className="font-medium text-gray-900 dark:text-white" title={item.marketName}>
                    {getShortMarketName(item.marketName)}
                  </p>
                  {/* Selection field not available in new API structure */}
                </div>
                <div className="text-right">
                  <p className="font-medium text-gray-900 dark:text-white">Rate: {item.rate}</p>
                  <p className="text-gray-600 dark:text-gray-400">
                    Stake: {formatSriLankanCurrency(item.stake)}
                  </p>
                  <SpkBadge variant="warning" size="sm">
                    Pending
                  </SpkBadge>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }, [apiData?.betList]);

  useEffect(() => {
    if (isOpen && autoCloseDelay > 0 && !isPaused) {
      setRemainingTime(autoCloseDelay);

      const interval = setInterval(() => {
        setRemainingTime(prev => {
          if (prev <= 1000) {
            handleAutoClose();
            return 0;
          }
          return prev - 1000;
        });
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [isOpen, autoCloseDelay, handleAutoClose, isPaused]);

  // Memoize backdrop click handler to prevent re-creation
  const handleBackdropClick = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if (showBackdrop && event.target === event.currentTarget) {
      onClose();
    }
  }, [showBackdrop, onClose]);
  // Enhanced rendering logic with better error handling
  if (!isOpen || !betDetails) {
    return null;
  }
  // Removed console logging to prevent performance issues

  return (
    <Fragment>
      <div
        className={`
          fixed inset-0 z-[99999]
          ${isOpen ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'}
          transition-all duration-300 ease-in-out
          ${className}
        `}
        role="dialog"
        aria-labelledby="bet-details-popup-title"
        aria-describedby="bet-details-popup-description"
        aria-hidden={!isOpen}
      >
        {/* Backdrop (optional) */}
        {showBackdrop && (
          <div
            className="fixed inset-0 bg-black bg-opacity-25 transition-opacity z-[99998]"
            onClick={handleBackdropClick}
            aria-hidden="true"
          />
        )}

        {/* Popup Container - Positioned as non-modal overlay */}
        <div className="fixed top-4 right-4 max-w-sm w-full pointer-events-auto z-[99999]">
          <div
            className={`
              bg-white dark:bg-gray-800 rounded-lg shadow-2xl border-2 border-green-500 dark:border-green-400
              transform transition-all duration-300 ease-in-out hover:shadow-3xl
              ${isOpen ? 'translate-x-0 scale-100' : 'translate-x-full scale-95'}
              ring-4 ring-green-500/20 dark:ring-green-400/20
            `}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}
          >
            {/* Header - Dynamic based on state */}
            <div className={`flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 rounded-t-lg ${apiError
              ? 'bg-red-50 dark:bg-red-900/20'
              : 'bg-green-50 dark:bg-green-900/20'
              }`}>
              <div className="flex items-center space-x-2">
                <div className="flex-shrink-0">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${apiError
                    ? 'bg-red-100 dark:bg-red-800'
                    : 'bg-green-100 dark:bg-green-800'
                    }`}>
                    {apiError ? (
                      <svg className="w-4 h-4 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                </div>
                <div>
                  <h3
                    id="bet-details-popup-title"
                    className="text-sm font-semibold text-gray-900 dark:text-white"
                  >
                    {apiError ? 'Bet Details Error' : 'Bet Placed Successfully'}
                  </h3>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    TurboStars Sportsbook
                  </p>
                </div>
              </div>
              <SpkButton
                type="button"
                customClass="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                onclickfunc={onClose}
                aria-label="Close popup"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </SpkButton>
            </div>

            {/* Body */}
            <div className="p-4 space-y-3" id="bet-details-popup-description">
              {/* Loading State */}
              {isLoadingDetails && (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-2 border-[#404040] border-t-primary"></div>
                  <span className="ml-2 text-sm text-text-secondary">Loading bet details...</span>
                </div>
              )}

              {/* Error State */}
              {apiError && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-red-600 dark:text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                    <span className="text-sm text-red-800 dark:text-red-200">{apiError}</span>
                  </div>
                </div>
              )}

              {/* API Data Display */}
              {apiData && !isLoadingDetails && (
                <>
                  {/* Market Information */}
                  <div className="bg-section rounded-lg p-3 space-y-2">
                    <h4 className="text-sm font-semibold text-text-primary">Market Information</h4>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="text-text-secondary">Market Name:</span>
                        <p className="font-medium text-text-primary" title={apiData.marketDetail?.marketName}>
                          {formatMarketName(apiData.marketDetail?.marketName || 'N/A')}
                        </p>
                      </div>
                      <div>
                        <span className="text-text-secondary">Status:</span>
                        <SpkBadge variant={apiData.marketDetail?.marketStatus === 'active' ? 'success' : 'warning'} size="sm">
                          {apiData.marketDetail?.marketStatus || 'N/A'}
                        </SpkBadge>
                      </div>
                    </div>
                  </div>

                  {/* Bet Information */}
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 space-y-2">
                    <h4 className="text-sm font-semibold text-gray-900 dark:text-white">Bet Details</h4>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">Bet Type:</span>
                        <p className="font-medium text-gray-900 dark:text-white">{apiData.betDetails?.betType || 'N/A'}</p>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">Amount:</span>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {formatSriLankanCurrency(apiData.betDetails?.betAmount || 0)}
                        </p>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">Settlement:</span>
                        <SpkBadge variant={apiData.betDetails?.settlementStatus === 'settled' ? 'success' : 'warning'} size="sm">
                          {apiData.betDetails?.settlementStatus || 'N/A'}
                        </SpkBadge>
                      </div>
                      <div>
                        <span className="text-gray-500 dark:text-gray-400">Date:</span>
                        <p className="font-medium text-gray-900 dark:text-white">
                          {formatUserDate(apiData.betDetails?.createdDate || '')}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Individual Bet Items - Memoized for performance */}
                  {renderedBetList}
                </>
              )}

              {/* Fallback to WebSocket Data */}
              {!apiData && !isLoadingDetails && !apiError && (
                <>
                  {/* User Information */}
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                      User ID
                    </span>
                    <SpkBadge variant="info" size="sm">
                      {betDetails.userId}
                    </SpkBadge>
                  </div>

                  {/* Market Information */}
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                      Market ID
                    </span>
                    <span className="text-sm font-mono text-text-primary bg-section px-2 py-1 rounded text-right">
                      {betDetails.marketId}
                    </span>
                  </div>

                  {/* Transaction Information */}
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                      Transaction ID
                    </span>
                    <span className="text-sm font-mono text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-right">
                      {betDetails.transactionId}
                    </span>
                  </div>

                  {/* Provider Information */}
                  <div className="flex items-center justify-between">
                    <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                      Provider
                    </span>
                    <SpkBadge variant="primary" size="sm">
                      {betDetails.provider}
                    </SpkBadge>
                  </div>
                </>
              )}

              {/* Timestamp */}
              <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-center">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {new Date().toLocaleString()}
                  </span>
                </div>
              </div>
            </div>

            {/* Footer with auto-close indicator */}
            {autoCloseDelay > 0 && (
              <div className="px-4 pb-3">
                <div className="text-center">
                  <span className="text-xs text-gray-400 dark:text-gray-500">
                    {isPaused ? (
                      <>
                        <span className="text-yellow-500">⏸</span> Paused • Press ESC to close
                      </>
                    ) : (
                      <>Auto-closes in {Math.ceil(remainingTime / 1000)}s • Press ESC to close</>
                    )}
                  </span>
                  {/* Progress bar for visual countdown */}
                  <div className="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                    <div
                      className={`h-1 rounded-full transition-all duration-1000 ease-linear ${isPaused ? 'bg-yellow-500' : 'bg-primary'
                        }`}
                      style={{
                        width: `${(remainingTime / autoCloseDelay) * 100}%`
                      }}
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export default BetDetailsPopup;
